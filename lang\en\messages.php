<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Messages Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain all user-facing messages used
    | throughout the application. This centralizes all text strings for
    | easy translation and maintenance.
    |
    */

    // General UI Messages
    'general' => [
        'dashboard' => 'Dashboard',
        'search' => 'Search...',
        'back' => 'Back',
        'continue' => 'Continue',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'delete' => 'Delete',
        'edit' => 'Edit',
        'view' => 'View',
        'download' => 'Download',
        'upload' => 'Upload',
        'submit' => 'Submit',
        'close' => 'Close',
        'confirm' => 'Confirm',
        'yes' => 'Yes',
        'no' => 'No',
        'loading' => 'Loading...',
        'processing' => 'Processing...',
        'please_wait' => 'Please wait...',
        'no_records_found' => 'No records found',
        'actions' => 'Actions',
        'status' => 'Status',
        'date' => 'Date',
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'address' => 'Address',
        'settings' => 'Settings',
        'logout' => 'Logout',
        'login' => 'Login',
        'register' => 'Register',
        'home' => 'Home',
        'add_new' => 'Add New',
        'download_all' => 'Download All',
        'select_all' => 'Select All',
        'clear_all' => 'Clear All',
        'refresh' => 'Refresh',
        'filter' => 'Filter',
        'export' => 'Export',
        'import' => 'Import',
        'print' => 'Print',
        'help' => 'Help',
        'about' => 'About',
        'contact' => 'Contact',
        'privacy' => 'Privacy',
        'terms' => 'Terms',
        'enter' => 'Enter :attribute',
    ],

    // Button Text
    'buttons' => [
        'add' => 'Add',
        'create' => 'Create',
        'update' => 'Update',
        'save_changes' => 'Save Changes',
        'delete_selected' => 'Delete Selected',
        'bulk_action' => 'Bulk Action',
        'send' => 'Send',
        'sign' => 'Sign',
        'approve' => 'Approve',
        'reject' => 'Reject',
        'reset' => 'Reset',
        'clear' => 'Clear',
        'apply' => 'Apply',
        'browse' => 'Browse',
        'choose_file' => 'Choose File',
        'upload_file' => 'Upload File',
        'download_pdf' => 'Download PDF',
        'view_details' => 'View Details',
        'edit_profile' => 'Edit Profile',
        'change_password' => 'Change Password',
        'forgot_password' => 'Forgot Password',
        'send_password' => 'Send Password',
        'temp_password' => 'Send Temporary Password',
        'keep_alive' => 'Keep Me Logged In',
        'log_out' => 'Log Out',
    ],

    // Form Labels and Placeholders
    'forms' => [
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'full_name' => 'Full Name',
        'email_address' => 'Email Address',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'current_password' => 'Current Password',
        'new_password' => 'New Password',
        'phone_number' => 'Phone Number',
        'date_of_birth' => 'Date of Birth',
        'age' => 'Age',
        'gender' => 'Gender',
        'address' => 'Address',
        'city' => 'City',
        'state' => 'State',
        'zip_code' => 'ZIP Code',
        'country' => 'Country',
        'role' => 'Role',
        'department' => 'Department',
        'position' => 'Position',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'description' => 'Description',
        'notes' => 'Notes',
        'comments' => 'Comments',
        'file' => 'File',
        'image' => 'Image',
        'document' => 'Document',
        'attachment' => 'Attachment',
        'select_option' => 'Select an option',
        'choose' => 'Choose...',
        'optional' => 'Optional',
        'required' => 'Required',
        'enter_email' => 'Enter your email address',
        'enter_password' => 'Enter your password',
        'search_placeholder' => 'Type to search...',
    ],

    // Success Messages
    'success' => [
        'created' => ':item has been created successfully.',
        'updated' => ':item has been updated successfully.',
        'deleted' => ':item has been deleted successfully.',
        'saved' => 'Changes have been saved successfully.',
        'sent' => ':item has been sent successfully.',
        'uploaded' => 'File has been uploaded successfully.',
        'downloaded' => 'File has been downloaded successfully.',
        'signed' => 'Document has been signed successfully.',
        'approved' => ':item has been approved successfully.',
        'rejected' => ':item has been rejected successfully.',
        'password_changed' => 'Password has been changed successfully.',
        'password_reset' => 'Password has been reset successfully.',
        'email_sent' => 'Email has been sent successfully.',
        'login_success' => 'You have been logged in successfully.',
        'logout_success' => 'You have been logged out successfully.',
        'profile_updated' => 'Profile has been updated successfully.',
        'settings_saved' => 'Settings have been saved successfully.',
        'import_completed' => 'Import has been completed successfully.',
        'export_completed' => 'Export has been completed successfully.',
        'operation_completed' => 'Operation completed successfully.',
        'data_synchronized' => 'Data has been synchronized successfully.',
        'backup_created' => 'Backup has been created successfully.',
        'restore_completed' => 'Restore has been completed successfully.',
    ],

    // Error Messages
    'errors' => [
        'general' => 'An error occurred. Please try again.',
        'not_found' => ':item not found.',
        'access_denied' => 'Access denied.',
        'unauthorized' => 'You are not authorized to perform this action.',
        'validation_failed' => 'Validation failed. Please check your input.',
        'file_not_found' => 'File not found.',
        'file_too_large' => 'File is too large. Maximum size is :size.',
        'invalid_file_type' => 'Invalid file type. Allowed types: :types.',
        'upload_failed' => 'File upload failed.',
        'download_failed' => 'File download failed.',
        'delete_failed' => 'Failed to delete :item.',
        'update_failed' => 'Failed to update :item.',
        'create_failed' => 'Failed to create :item.',
        'save_failed' => 'Failed to save changes.',
        'send_failed' => 'Failed to send :item.',
        'login_failed' => 'Login failed. Please check your credentials.',
        'password_incorrect' => 'Current password is incorrect.',
        'password_mismatch' => 'Passwords do not match.',
        'email_exists' => 'Email address already exists.',
        'invalid_email' => 'Invalid email address.',
        'required_field' => 'This field is required.',
        'invalid_format' => 'Invalid format.',
        'database_error' => 'Database error occurred.',
        'server_error' => 'Server error occurred.',
        'network_error' => 'Network error occurred.',
        'timeout_error' => 'Request timeout.',
        'permission_denied' => 'Permission denied.',
        'session_expired' => 'Your session has expired. Please login again.',
        'account_disabled' => 'Your account has been disabled. Please contact the administrator.',
        'account_locked' => 'Your account has been locked due to multiple failed login attempts.',
        'maintenance_mode' => 'System is currently under maintenance.',
        'service_unavailable' => 'Service is currently unavailable.',
    ],

    // Warning Messages
    'warnings' => [
        'unsaved_changes' => 'You have unsaved changes. Are you sure you want to leave?',
        'delete_confirmation' => 'Are you sure you want to delete this :item?',
        'permanent_action' => 'This action cannot be undone.',
        'data_loss' => 'This action may result in data loss.',
        'overwrite_file' => 'A file with this name already exists. Do you want to overwrite it?',
        'large_file' => 'This file is large and may take time to process.',
        'session_timeout' => 'Your session will expire in :minutes minutes.',
        'password_expiry' => 'Your password will expire in :days days.',
        'account_expiry' => 'Your account will expire in :days days.',
        'maintenance_scheduled' => 'System maintenance is scheduled for :date.',
        'beta_feature' => 'This is a beta feature and may not work as expected.',
        'experimental' => 'This feature is experimental.',
    ],

    // Info Messages
    'info' => [
        'no_data' => 'No data available.',
        'empty_list' => 'The list is empty.',
        'first_time' => 'Welcome! This is your first time here.',
        'tutorial_available' => 'Tutorial is available in the help section.',
        'feature_coming_soon' => 'This feature is coming soon.',
        'under_development' => 'This feature is under development.',
        'contact_support' => 'Contact support for assistance.',
        'check_documentation' => 'Check the documentation for more information.',
        'system_update' => 'System has been updated to version :version.',
        'new_features' => 'New features are available.',
        'performance_improved' => 'Performance has been improved.',
        'security_enhanced' => 'Security has been enhanced.',
    ],

    // Excel Import Messages
    'excel' => [
        'upload_title' => 'Excel Import',
        'choose_file' => 'Choose Excel File',
        'upload_button' => 'Upload File',
        'preview_title' => 'Data Preview',
        'processing_title' => 'Processing Data',
        'completed_title' => 'Import Completed',
        'rows_read' => ':count rows read successfully',
        'rows_error' => ':count rows with errors',
        'individual_errors' => ':count individual cell errors',
        'blank_row' => 'Blank row',
        'missing_value' => 'missing',
        'invalid_date' => 'Invalid date format',
        'required_field_missing' => 'A required value is missing. Please complete all necessary fields.',
        'blank_row_detected' => 'Blank row detected',
        'cannot_continue' => 'Cannot continue - all rows contain errors',
        'no_file_uploaded' => 'Please upload an Excel file to continue.',
        'file_processed' => 'File has been processed successfully.',
        'validation_errors' => 'Please fix the validation errors before continuing.',
        'no_data_found' => 'No data was found beyond row 2 in the Excel file.',
        'empty_file' => 'The uploaded file is empty.',
        'invalid_format' => 'Invalid file format. Please upload an Excel file.',
        'file_corrupted' => 'The file appears to be corrupted.',
        'too_many_rows' => 'File contains too many rows. Maximum allowed: :max',
        'too_many_columns' => 'File contains too many columns. Maximum allowed: :max',
    ],

    // Script Messages
    'scripts' => [
        'title' => 'Scripts',
        'all_scripts' => 'All Scripts',
        'ready_to_send' => 'Ready to Send',
        'sent_scripts' => 'Sent Scripts',
        'pending_approval' => 'Pending Approval',
        'pending_dispatch' => 'Pending Dispatch',
        'new_status' => 'New',
        'signed' => 'Signed',
        'sent' => 'Sent',
        'script_date' => 'Script Date',
        'patient_name' => 'Patient Name',
        'provider' => 'Provider',
        'pharmacy' => 'Pharmacy',
        'medication' => 'Medication',
        'dosage' => 'Dosage',
        'quantity' => 'Quantity',
        'refills' => 'Refills',
        'signature' => 'Signature',
        'signed_at' => 'Signed At',
        'sent_at' => 'Sent At',
        'created_at' => 'Created At',
        'updated_at' => 'Updated At',
        'view_script' => 'View Script',
        'download_script' => 'Download Script',
        'sign_script' => 'Sign Script',
        'send_script' => 'Send Script',
        'delete_script' => 'Delete Script',
        'bulk_sign' => 'Bulk Sign',
        'bulk_send' => 'Bulk Send',
        'bulk_delete' => 'Bulk Delete',
        'no_script_found' => 'No script found.',
        'script_signed' => 'Script has been signed successfully.',
        'script_sent' => 'Script has been sent successfully.',
        'script_deleted' => 'Script has been deleted successfully.',
        'scripts_signed' => ':count scripts have been signed successfully.',
        'scripts_sent' => ':count scripts have been sent successfully.',
        'scripts_deleted' => ':count scripts have been deleted successfully.',
        'fax_queued' => 'Fax queued for dispatch. Status changed to \'Pending Dispatch\' until delivery is confirmed.',
        'cannot_delete_sent' => 'Cannot delete scripts that have been sent.',
        'cannot_modify_sent' => 'Cannot modify scripts that have been sent.',
        'sign_required' => 'Script must be signed before sending.',
        'approval_required' => 'Script requires approval before sending.',
    ],

    // User Management Messages
    'users' => [
        'title' => 'Users',
        'add_user' => 'Add User',
        'edit_user' => 'Edit User',
        'user_profile' => 'User Profile',
        'user_details' => 'User Details',
        'personal_info' => 'Personal Information',
        'contact_info' => 'Contact Information',
        'account_info' => 'Account Information',
        'role_permissions' => 'Role & Permissions',
        'account_status' => 'Account Status',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',
        'pending' => 'Pending',
        'administrator' => 'Administrator',
        'operator' => 'Operator',
        'provider' => 'Provider',
        'staff' => 'Staff',
        'user_created' => 'User has been created successfully.',
        'user_updated' => 'User has been updated successfully.',
        'user_deleted' => 'User has been deleted successfully.',
        'password_sent' => 'Temporary password has been sent to the user.',
        'account_activated' => 'Account has been activated.',
        'account_deactivated' => 'Account has been deactivated.',
        'cannot_delete_self' => 'You cannot delete your own account.',
        'cannot_deactivate_self' => 'You cannot deactivate your own account.',
        'email_already_exists' => 'A user with this email address already exists.',
        'invalid_role' => 'Invalid user role selected.',
    ],

    // Authentication Messages
    'auth' => [
        'login_title' => 'Sign In',
        'register_title' => 'Sign Up',
        'forgot_password_title' => 'Forgot Password',
        'reset_password_title' => 'Reset Password',
        'change_password_title' => 'Change Password',
        'welcome_back' => 'Welcome back!',
        'sign_in_account' => 'Sign in to your account',
        'create_account' => 'Create your account',
        'remember_me' => 'Remember me',
        'forgot_password_link' => 'Forgot your password?',
        'already_have_account' => 'Already have an account?',
        'dont_have_account' => 'Don\'t have an account?',
        'sign_up_here' => 'Sign up here',
        'sign_in_here' => 'Sign in here',
        'enter_email_reset' => 'Enter your email address to reset your password',
        'password_reset_sent' => 'Password reset link has been sent to your email.',
        'password_reset_success' => 'Your password has been reset successfully.',
        'login_success' => 'You have been logged in successfully.',
        'logout_success' => 'You have been logged out successfully.',
        'registration_success' => 'Your account has been created successfully.',
        'invalid_credentials' => 'Invalid email or password.',
        'account_not_verified' => 'Your account is not verified.',
        'account_suspended' => 'Your account has been suspended.',
        'too_many_attempts' => 'Too many login attempts. Please try again later.',
        'password_changed_success' => 'Your password has been changed successfully.',
        'current_password_incorrect' => 'Current password is incorrect.',
        'password_requirements' => 'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.',
        'session_expired' => 'Your session has expired. Please login again.',
        'unauthorized_access' => 'You are not authorized to access this page.',
        'force_password_change' => 'You must change your password before continuing.',
    ],

    // Modal Messages
    'modals' => [
        'confirm_delete' => 'Confirm Delete',
        'confirm_action' => 'Confirm Action',
        'are_you_sure' => 'Are you sure?',
        'delete_warning' => 'You won\'t be able to revert this!',
        'yes_delete' => 'Yes, delete it!',
        'yes_continue' => 'Yes, continue!',
        'cancel_action' => 'Cancel',
        'deleted_success' => 'Deleted!',
        'action_completed' => 'Action completed successfully.',
        'operation_cancelled' => 'Operation cancelled.',
        'close_modal' => 'Close',
        'save_changes' => 'Save Changes',
        'discard_changes' => 'Discard Changes',
        'unsaved_changes_warning' => 'You have unsaved changes. Do you want to save them?',
        'loading_data' => 'Loading data...',
        'processing_request' => 'Processing your request...',
        'please_wait' => 'Please wait...',
        'error_occurred' => 'An error occurred',
        'try_again' => 'Please try again',
        'contact_support' => 'If the problem persists, please contact support.',
    ],

    // Tooltip Messages
    'tooltips' => [
        'view' => 'View details',
        'edit' => 'Edit this item',
        'delete' => 'Delete this item',
        'download' => 'Download file',
        'upload' => 'Upload file',
        'sign' => 'Sign document',
        'send' => 'Send via fax',
        'approve' => 'Approve this item',
        'reject' => 'Reject this item',
        'refresh' => 'Refresh data',
        'filter' => 'Filter results',
        'search' => 'Search records',
        'export' => 'Export data',
        'import' => 'Import data',
        'print' => 'Print document',
        'copy' => 'Copy to clipboard',
        'share' => 'Share this item',
        'bookmark' => 'Bookmark this page',
        'help' => 'Get help',
        'settings' => 'Open settings',
        'profile' => 'View profile',
        'logout' => 'Sign out',
        'back_to_top' => 'Back to top',
        'expand' => 'Expand section',
        'collapse' => 'Collapse section',
        'fullscreen' => 'Enter fullscreen',
        'exit_fullscreen' => 'Exit fullscreen',
        'required_field' => 'This field is required',
        'optional_field' => 'This field is optional',
        'invalid_input' => 'Invalid input format',
        'password_strength' => 'Password strength indicator',
        'file_size_limit' => 'Maximum file size: :size',
        'allowed_formats' => 'Allowed formats: :formats',
    ],

    // Table Messages
    'table' => [
        'no_data' => 'No data available in table',
        'loading' => 'Loading...',
        'processing' => 'Processing...',
        'search' => 'Search:',
        'show_entries' => 'Show _MENU_ entries',
        'showing' => 'Showing _START_ to _END_ of _TOTAL_ entries',
        'showing_filtered' => 'Showing _START_ to _END_ of _TOTAL_ entries (filtered from _MAX_ total entries)',
        'empty_table' => 'No matching records found',
        'info_empty' => 'Showing 0 to 0 of 0 entries',
        'first' => 'First',
        'previous' => 'Previous',
        'next' => 'Next',
        'last' => 'Last',
        'page' => 'Page',
        'of' => 'of',
        'all' => 'All',
        'select_all' => 'Select all',
        'deselect_all' => 'Deselect all',
        'selected_items' => ':count items selected',
        'bulk_actions' => 'Bulk Actions',
        'apply_action' => 'Apply',
        'sort_ascending' => 'Sort ascending',
        'sort_descending' => 'Sort descending',
        'column_visibility' => 'Column visibility',
        'export_csv' => 'Export CSV',
        'export_excel' => 'Export Excel',
        'export_pdf' => 'Export PDF',
        'print_table' => 'Print',
    ],

    // Fax Messages
    'fax' => [
        'title' => 'Fax Management',
        'send_fax' => 'Send Fax',
        'fax_number' => 'Fax Number',
        'fax_label' => 'Fax Label',
        'destination' => 'Destination',
        'add_fax_number' => 'Add Fax Number',
        'edit_fax_number' => 'Edit Fax Number',
        'delete_fax_number' => 'Delete Fax Number',
        'fax_options' => 'Fax Options',
        'fax_settings' => 'Fax Settings',
        'default_fax' => 'Default Fax Number',
        'fax_sent' => 'Fax sent successfully',
        'fax_failed' => 'Failed to send fax',
        'fax_queued' => 'Fax queued for sending',
        'fax_delivered' => 'Fax delivered successfully',
        'fax_pending' => 'Fax delivery pending',
        'invalid_fax_number' => 'Invalid fax number format',
        'fax_number_required' => 'Fax number is required',
        'fax_label_required' => 'Fax label is required',
        'fax_number_exists' => 'This fax number already exists',
        'no_fax_configured' => 'No fax numbers configured',
        'configure_fax' => 'Please configure fax settings first',
        'fax_history' => 'Fax History',
        'sent_at' => 'Sent At',
        'delivery_status' => 'Delivery Status',
        'retry_fax' => 'Retry Fax',
        'fax_log' => 'Fax Log',
    ],

    // Settings Messages
    'settings' => [
        'title' => 'Settings',
        'general_settings' => 'General Settings',
        'account_settings' => 'Account Settings',
        'security_settings' => 'Security Settings',
        'notification_settings' => 'Notification Settings',
        'system_settings' => 'System Settings',
        'user_preferences' => 'User Preferences',
        'application_settings' => 'Application Settings',
        'email_settings' => 'Email Settings',
        'backup_settings' => 'Backup Settings',
        'maintenance_settings' => 'Maintenance Settings',
        'change_password' => 'Change Password',
        'two_factor_auth' => 'Two-Factor Authentication',
        'login_history' => 'Login History',
        'session_management' => 'Session Management',
        'privacy_settings' => 'Privacy Settings',
        'data_export' => 'Data Export',
        'account_deletion' => 'Account Deletion',
        'settings_saved' => 'Settings have been saved successfully.',
        'settings_reset' => 'Settings have been reset to default.',
        'invalid_setting' => 'Invalid setting value.',
        'setting_updated' => 'Setting has been updated.',
        'backup_created' => 'Backup has been created successfully.',
        'backup_restored' => 'Backup has been restored successfully.',
        'maintenance_mode_enabled' => 'Maintenance mode has been enabled.',
        'maintenance_mode_disabled' => 'Maintenance mode has been disabled.',
    ],

    // Progress Steps
    'progress' => [
        'step' => 'Step',
        'of' => 'of',
        'upload' => 'Upload',
        'create' => 'Create',
        'sign' => 'Sign',
        'done' => 'Done',
        'preview' => 'Preview',
        'process' => 'Process',
        'complete' => 'Complete',
        'validate' => 'Validate',
        'review' => 'Review',
        'confirm' => 'Confirm',
        'submit' => 'Submit',
        'pending' => 'Pending',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
        'skipped' => 'Skipped',
        'current_step' => 'Current Step',
        'next_step' => 'Next Step',
        'previous_step' => 'Previous Step',
        'step_completed' => 'Step completed',
        'step_failed' => 'Step failed',
        'all_steps_completed' => 'All steps completed successfully',
        'progress_saved' => 'Progress has been saved',
    ],

    // Logs Messages
    'logs' => [
        'title' => 'System Logs',
        'log_entry' => 'Log Entry',
        'timestamp' => 'Timestamp',
        'type' => 'Type',
        'user' => 'User',
        'message' => 'Message',
        'details' => 'Details',
        'context' => 'Context',
        'level' => 'Level',
        'category' => 'Category',
        'source' => 'Source',
        'ip_address' => 'IP Address',
        'user_agent' => 'User Agent',
        'session_id' => 'Session ID',
        'request_id' => 'Request ID',
        'error' => 'Error',
        'warning' => 'Warning',
        'info' => 'Info',
        'debug' => 'Debug',
        'critical' => 'Critical',
        'emergency' => 'Emergency',
        'alert' => 'Alert',
        'notice' => 'Notice',
        'filter_logs' => 'Filter Logs',
        'export_logs' => 'Export Logs',
        'clear_logs' => 'Clear Logs',
        'log_cleared' => 'Logs have been cleared successfully.',
        'log_exported' => 'Logs have been exported successfully.',
        'no_logs_found' => 'No logs found for the selected criteria.',
        'log_details' => 'Log Details',
        'view_log' => 'View Log',
        'download_log' => 'Download Log',
    ],

];
