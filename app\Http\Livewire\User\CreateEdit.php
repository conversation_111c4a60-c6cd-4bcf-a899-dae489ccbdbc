<?php

namespace App\Http\Livewire\User;

use App\Mail\ForgotPasswordLinkMail;
use App\Mail\WelcomeUserMail;
use App\Models\State;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;


class CreateEdit extends Component
{
    use WithFileUploads;
    public User $user;
    public $signature;
    public $states = [];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadStates();

        // For new users, initialize signature as null
        if (!$user->id) {
            $this->signature = null;
        } else {
            // For existing users, log the signature path for debugging
            if ($user->signature) {
                \Illuminate\Support\Facades\Log::info('User signature path', [
                    'user_id' => $user->id,
                    'signature_path' => $user->signature,
                    'exists' => \Illuminate\Support\Facades\Storage::disk('public')->exists($user->signature),
                    'full_path' => storage_path('app/public/' . $user->signature)
                ]);
            }
        }
        // The $signature property will only be set if the user uploads a new signature
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function rules()
    {
        return [
            'user.first_name' => 'required|max:255',
            'user.email' => $this->user->id ? ('required|email|unique:users,email,' . $this->user->id) : 'required|email|unique:users,email',
            'user.password' => '',
            'user.last_name' => 'required|max:255',
            'user.printed_name' => 'required|max:255',
            'user.clinic_name' => 'nullable|max:255',
            'user.NPI#' => 'nullable|digits:10',
            'user.LIC#' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    if ($value && strlen(str_replace('-', '', $value)) < 7) {
                        $fail('The LIC# must have at least 7 characters excluding dashes.');
                    }
                },
            ],

            'user.DEA#' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Remove any spaces or special characters
                        $cleanValue = preg_replace('/[^a-zA-Z0-9]/', '', $value);

                        // Check if the value has at least 2 letters followed by at least 7 digits
                        if (!preg_match('/^[a-zA-Z]{2}[0-9]{7}$/', $cleanValue)) {
                            $fail('DEA must be 9 characters: two letters followed by seven digits.');
                        }
                    }
                }
            ],
            'user.phone' => [
                'nullable',
                'regex:/^[2-9][0-9]{9}$/'
            ],
            'user.fax' => [
                'nullable',
                'regex:/^[2-9][0-9]{9}$/'
            ],
            'user.address' => 'nullable',
            'user.city' => 'nullable|max:255',
            'user.state_id' => 'nullable',
            'user.zip' => 'nullable|max:255',
            'user.is_active' => 'boolean|nullable',
            // Only require signature for new users
            'signature' => $this->user->id ? 'nullable|image|mimes:jpg,jpeg,png,gif,webp,bmp|max:2048' : 'required|image|mimes:jpg,jpeg,png,gif,webp,bmp|max:2048',
        ];
    }
    public function messages()
    {
        return [
            'user.NPI#.required' => 'The NPI# field is required.',
            'user.NPI#.digits' => 'The NPI# must be 10 digits.',
            'user.LIC#.required' => 'The LIC# field is required.',
            'user.state_id.required' => 'The State field is required.',
            'user.DEA#.required' => 'The DEA# field is required.',
            'user.phone.regex' => 'Phone number must be exactly 10 digits and cannot start with 0 or 1.',
            'user.fax.regex' => 'Fax number must be exactly 10 digits and cannot start with 0 or 1.',
        ];
    }

    // public function update($propertyName)
    // {
    //     $this->validateOnly($propertyName);
    // }

    public function store()
    {
        // Ensure email is trimmed before validation
        if ($this->user->email) {
            $this->user->email = trim($this->user->email);
        }

        $this->validate();
        try {
            // Check if this is an existing user and if the email has been changed
            $emailChanged = false;
            if ($this->user->id) {
                $originalUser = User::find($this->user->id);
                if ($originalUser && $originalUser->email !== $this->user->email) {
                    $emailChanged = true;
                }
            }

            // Format fields to capitalize letters when form is submitted
            $this->user->{'LIC#'} = $this->formatLicenseNumber($this->user->{'LIC#'});

            // Format DEA# to ensure it's in the correct format (2 uppercase letters + 7 digits)
            $this->user->{'DEA#'} = $this->formatDEANumber($this->user->{'DEA#'});

            // Handle signature upload
            if ($this->signature) {
                // Delete old signature if it exists and is not a default image
                if (isset($this->user->id) && $this->user->signature && !str_contains($this->user->signature, 'default')) {
                    try {
                        Storage::delete($this->user->signature);
                    } catch (\Throwable $th) {
                        // Silently handle deletion errors
                    }
                }

                try {
                    // Store the new signature in the public disk with error handling
                    $path = $this->signature->store('signatures', 'public');

                    if (!$path) {
                        throw new \Exception("Failed to store signature file");
                    }

                    $this->user->signature = $path;
                } catch (\Throwable $e) {
                    session()->flash('error-message', "Error uploading signature: " . $e->getMessage());
                    return redirect()->back();
                }
            }

            // Check if an admin already exists
            $is_admin = User::where('role', User::ROLE_ADMIN)->first();

            // Prevent more than one admin user
            if (isset($this->user->role) && strtolower($this->user->role) === User::ROLE_ADMIN && $is_admin) {
                session()->flash('error-message', "Admin already exists.");
            } else {
                // Set role to 'provider' if not set
                $this->user->role = User::ROLE_PROVIDER;

                // Only for new users: set password and send welcome email
                if (!$this->user->id) {
                    $randomPassword = Str::random(8);
                    $this->user->password = bcrypt($randomPassword);
                    $this->user->password_changed_at = null; // Set to null to force password change on first login

                    // Save the user to the database
                    $this->user->save();

                    // Log provider creation
                    LogService::logProviderCreated($this->user);

                    // Send welcome email with credentials
                    try {
                        Mail::to($this->user->email)
                            ->send(new WelcomeUserMail($this->user, $randomPassword));

                        // Log temporary password sent
                        LogService::logTempPasswordSent($this->user);

                        $message = "Provider created successfully. Login credentials have been sent to {$this->user->email}";
                    } catch (\Throwable $e) {
                        $message = "User created successfully, but the notification email could not be sent. Please provide the temporary password separately: " . "password";
                    }
                } else {
                    // Get original values before saving for change tracking
                    $originalValues = [];
                    if ($this->user && method_exists($this->user, 'getOriginal')) {
                        $originalValues = LogService::getModelAttributes($this->user->getOriginal(), [
                            'first_name', 'last_name', 'email', 'phone', 'license_number',
                            'dea_number', 'npi_number', 'is_active'
                        ]);
                    }

                    // Just save the user for updates
                    $this->user->save();

                    // Get new values after saving
                    $newValues = LogService::getModelAttributes($this->user, [
                        'first_name', 'last_name', 'email', 'phone', 'license_number',
                        'dea_number', 'npi_number', 'is_active'
                    ]);

                    // Log provider editing with changes
                    LogService::logProviderEdited($this->user, $originalValues, $newValues);

                    // If email was changed, send a reset password email
                    if ($emailChanged) {
                        // Generate a new random password
                        $randomPassword = Str::random(8);

                        // Update the user's password
                        $this->user->password = bcrypt($randomPassword);
                        $this->user->password_changed_at = null; // Force password change on next login
                        $this->user->save();

                        try {
                            // Send the reset password email
                            Mail::to($this->user->email)->send(new ForgotPasswordLinkMail($randomPassword, $this->user));

                            // Log temporary password sent
                            LogService::logTempPasswordSent($this->user);

                            $message = "User updated successfully. A temporary password has been sent to the new email address.";
                        } catch (\Exception $e) {
                            // Log the error
                            Log::error('Failed to send temporary password email after email change', [
                                'email' => $this->user->email,
                                'error' => $e->getMessage()
                            ]);
                            $message = "User updated successfully, but failed to send password email to the new address.";
                        }
                    } else {
                        $message = "User updated successfully.";
                    }
                }

                session()->flash('success-message', $message);
            }
        } catch (\Throwable $th) {
            session()->flash('error-message', $th->getMessage());
        }

        return redirect()->route('users.index');
    }
    public function render()
    {
        return view('livewire.user.create-edit');
    }

    public function updated($propertyName)
    {
        // Trim email when it's updated
        if ($propertyName === 'user.email' && $this->user->email) {
            $this->user->email = trim($this->user->email);
        }

        $this->validateOnly($propertyName);

        // Emit contentChanged event to re-initialize Select2 after property updates
        $this->emit('contentChanged');
    }

    /**
     * Format license numbers to capitalize letters while preserving numbers and hyphens
     *
     * @param string $value
     * @return string
     */
    private function formatLicenseNumber($value)
    {
        if (empty($value)) {
            return $value;
        }

        // Use a callback function with preg_replace_callback to capitalize only the letters
        return preg_replace_callback('/[a-zA-Z]+/', function ($matches) {
            return strtoupper($matches[0]);
        }, $value);
    }

    /**
     * Capitalize the first letter of each word in a string
     *
     * @param string $value
     * @return string
     */
    private function capitalizeWords($value)
    {
        if (empty($value)) {
            return $value;
        }

        // Convert to title case (first letter of each word capitalized)
        // This preserves capitalization in the middle of words (like "McDonald")
        return ucwords(strtolower($value));
    }

    /**
     * Format DEA number to ensure it's in the correct format (2 uppercase letters + 7 digits)
     *
     * @param string $value
     * @return string
     */
    private function formatDEANumber($value)
    {
        if (empty($value)) {
            return $value;
        }

        // Remove any spaces or special characters
        $cleanValue = preg_replace('/[^a-zA-Z0-9]/', '', $value);

        // Extract the first 2 characters (letters) and convert to uppercase
        $letters = strtoupper(substr($cleanValue, 0, 2));

        // Extract the remaining characters (should be digits)
        $digits = substr($cleanValue, 2);

        // Combine and return
        return $letters . $digits;
    }
}
