<?php

namespace Database\Seeders;

use App\Models\SystemLog;
use App\Models\User;
use Illuminate\Database\Seeder;

class SystemLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users for testing
        $admin = User::where('role', User::ROLE_ADMIN)->first();
        $operator = User::where('role', User::ROLE_OPERATOR)->first();
        $provider = User::where('role', User::ROLE_PROVIDER)->first();

        // Create sample log entries
        $logs = [
            [
                'timestamp' => now()->subDays(5),
                'type' => SystemLog::TYPE_LOGIN,
                'user_type' => $admin ? $admin->role : User::ROLE_ADMIN,
                'username' => $admin ? "{$admin->first_name} {$admin->last_name}" : 'Admin User',
                'user_id' => $admin ? $admin->id : null,
                'message' => 'User logged in',
                'context' => null,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subDays(4),
                'type' => SystemLog::TYPE_PROVIDER_CREATED,
                'user_type' => $admin ? $admin->role : User::ROLE_ADMIN,
                'username' => $admin ? "{$admin->first_name} {$admin->last_name}" : 'Admin User',
                'user_id' => $admin ? $admin->id : null,
                'message' => 'Provider created: John Doe',
                'context' => ['provider_id' => 1, 'provider_email' => '<EMAIL>'],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subDays(3),
                'type' => SystemLog::TYPE_SCRIPT_CREATED,
                'user_type' => $provider ? $provider->role : User::ROLE_PROVIDER,
                'username' => $provider ? "{$provider->first_name} {$provider->last_name}" : 'Provider User',
                'user_id' => $provider ? $provider->id : null,
                'message' => 'Script created for Jane Smith',
                'context' => ['patient_name' => 'Jane Smith', 'medication' => 'Lisinopril', 'script_id' => 1],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subDays(2),
                'type' => SystemLog::TYPE_SCRIPT_SIGNED,
                'user_type' => $provider ? $provider->role : User::ROLE_PROVIDER,
                'username' => $provider ? "{$provider->first_name} {$provider->last_name}" : 'Provider User',
                'user_id' => $provider ? $provider->id : null,
                'message' => 'Script signed for Jane Smith',
                'context' => ['patient_name' => 'Jane Smith', 'medication' => 'Lisinopril', 'script_id' => 1],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subDays(1),
                'type' => SystemLog::TYPE_SCRIPT_SENT,
                'user_type' => $operator ? $operator->role : User::ROLE_OPERATOR,
                'username' => $operator ? "{$operator->first_name} {$operator->last_name}" : 'Operator User',
                'user_id' => $operator ? $operator->id : null,
                'message' => 'Script sent for Jane Smith',
                'context' => ['patient_name' => 'Jane Smith', 'medication' => 'Lisinopril', 'script_id' => 1, 'fax_number' => '555-0123'],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subHours(12),
                'type' => SystemLog::TYPE_PROVIDER_DEACTIVATED,
                'user_type' => $admin ? $admin->role : User::ROLE_ADMIN,
                'username' => $admin ? "{$admin->first_name} {$admin->last_name}" : 'Admin User',
                'user_id' => $admin ? $admin->id : null,
                'message' => 'Provider deactivated: John Doe',
                'context' => ['provider_id' => 1, 'provider_email' => '<EMAIL>'],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subHours(6),
                'type' => SystemLog::TYPE_SYSTEM_ERROR,
                'user_type' => null,
                'username' => null,
                'user_id' => null,
                'message' => 'Failed to send fax: Connection timeout',
                'context' => ['error_code' => 'TIMEOUT', 'fax_number' => '555-0123', 'script_id' => 1],
                'ip_address' => null,
                'user_agent' => null,
            ],
            [
                'timestamp' => now()->subHours(2),
                'type' => SystemLog::TYPE_PASSWORD_CHANGED,
                'user_type' => $provider ? $provider->role : User::ROLE_PROVIDER,
                'username' => $provider ? "{$provider->first_name} {$provider->last_name}" : 'Provider User',
                'user_id' => $provider ? $provider->id : null,
                'message' => 'Password changed',
                'context' => null,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subMinutes(30),
                'type' => SystemLog::TYPE_SETTINGS_UPDATED,
                'user_type' => $admin ? $admin->role : User::ROLE_ADMIN,
                'username' => $admin ? "{$admin->first_name} {$admin->last_name}" : 'Admin User',
                'user_id' => $admin ? $admin->id : null,
                'message' => 'Settings updated: Fax Options',
                'context' => ['setting_type' => 'fax_options', 'changes' => ['default_fax_number' => '555-0199']],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subMinutes(15),
                'type' => SystemLog::TYPE_PROVIDER_EDITED,
                'user_type' => $admin ? $admin->role : User::ROLE_ADMIN,
                'username' => $admin ? "{$admin->first_name} {$admin->last_name}" : 'Admin User',
                'user_id' => $admin ? $admin->id : null,
                'message' => 'Provider edited: John Doe',
                'context' => [
                    'provider_id' => 1,
                    'provider_email' => '<EMAIL>',
                    'changes' => [
                        'first_name' => ['old' => 'John', 'new' => 'Jonathan'],
                        'phone' => ['old' => '555-1234', 'new' => '555-5678'],
                        'license_number' => ['old' => 'LIC123', 'new' => 'LIC456'],
                        'is_active' => ['old' => false, 'new' => true]
                    ]
                ],
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
            [
                'timestamp' => now()->subMinutes(5),
                'type' => SystemLog::TYPE_LOGOUT,
                'user_type' => $admin ? $admin->role : User::ROLE_ADMIN,
                'username' => $admin ? "{$admin->first_name} {$admin->last_name}" : 'Admin User',
                'user_id' => $admin ? $admin->id : null,
                'message' => 'User logged out',
                'context' => null,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ],
        ];

        foreach ($logs as $log) {
            SystemLog::create($log);
        }
    }
}
